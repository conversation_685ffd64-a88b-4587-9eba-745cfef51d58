package be.fgov.onerva.person.backend.request.service;

import be.fgov.onerva.person.backend.citizen.model.CitizenUpdateRequest;
import be.fgov.onerva.person.backend.request.event.PersonRequestEvent;
import be.fgov.onerva.person.backend.request.formatter.MainframeUpdateMessageFormatter;
import be.fgov.onerva.person.backend.request.model.PersonRequest;
import be.fgov.onerva.person.backend.request.model.PersonRequestType;
import be.fgov.onerva.person.backend.request.persistence.PersonRequestRepository;
import be.fgov.onerva.wave.model.User;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Slf4j
@RequiredArgsConstructor
@Service
public class PersonRequestService {

    private final PersonRequestRepository repository;
    private final ApplicationEventPublisher eventPublisher;
    private final MainframeUpdateMessageFormatter messageFormatter;

    @Transactional("personTransactionManager")
    public PersonRequest createMinimalPersonInfo(String firstname, String lastname, String inss, String correlationId) {
        var saved = repository.saveAndFlush(PersonRequest.builder()
                .type(PersonRequestType.CREATE)
                .niss(inss)
                .firstname(firstname)
                .lastname(lastname)
                .correlationId(correlationId)
                .created(LocalDateTime.now())
                .build());

        eventPublisher.publishEvent(new PersonRequestEvent(saved));

        return saved;
    }

    @Transactional("personTransactionManager")
    public PersonRequest updatePersonInfo(CitizenUpdateRequest update, User user) {
        var saved = repository.saveAndFlush(PersonRequest.builder()
                .type(PersonRequestType.UPDATE)
                .niss(update.getNiss())
                .nationalityCode(update.getNationalityCode())
                .paymentType(update.getPaymentType())
                .unionDue(update.getUnionDue())
                .valueDate(update.getValueDate())
                .address(update.getAddress())
                .username(user.getUsername())
                .operatorCode(getOperatorCode(user))
                .correlationId(update.getCorrelationId())
                .created(LocalDateTime.now())
                .build());

        eventPublisher.publishEvent(new PersonRequestEvent(saved));

        return saved;
    }

    Integer getOperatorCode(User user) {
        if (!user.getOperatorCodes().isEmpty()) {
            try {
                return Integer.parseInt(user.getOperatorCodes().getFirst());
            } catch (NumberFormatException e) {
                log.warn("Could not convert operator code to an int! ... {}", user);
            }
        }
        return null;
    }
}
